// src/services/globalTimelineSync.ts
// Global service to expose timeline sync functionality to the entire app

import { storyTimelineSync } from './storyTimelineSync';
import { projectDataManager } from '../utils/projectDataManager';
import { 
  loadMezzanineScenesToTimeline, 
  createSeamlessTransitionConfig 
} from '../utils/mezzanineLoader';

export class GlobalTimelineSync {
  private static instance: GlobalTimelineSync;

  private constructor() {}

  static getInstance(): GlobalTimelineSync {
    if (!GlobalTimelineSync.instance) {
      GlobalTimelineSync.instance = new GlobalTimelineSync();
    }
    return GlobalTimelineSync.instance;
  }

  // Get current analysis ID from various sources
  private getCurrentAnalysisId(): string | null {
    // Try to get from global window object (set by App component)
    const globalAnalysisId = (window as any).currentAnalysisId;
    if (globalAnalysisId) {
      return globalAnalysisId;
    }

    // Try to get from project data manager (most reliable after refresh)
    try {
      const project = projectDataManager.getCurrentProject();
      if (project?.analysisId) {
        console.log('🎯 Found analysis ID from project data:', project.analysisId);
        // Set it globally for future use
        (window as any).currentAnalysisId = project.analysisId;
        return project.analysisId;
      }
    } catch (error) {
      console.warn('Failed to get analysis ID from project data:', error);
    }

    // Fallback: try to get from localStorage
    const storedAnalysisData = localStorage.getItem('currentAnalysisData');
    if (storedAnalysisData) {
      try {
        const analysisData = JSON.parse(storedAnalysisData);
        if (analysisData.analysisId) {
          // Set it globally for future use
          (window as any).currentAnalysisId = analysisData.analysisId;
          return analysisData.analysisId;
        }
      } catch (error) {
        console.warn('Failed to parse stored analysis data:', error);
      }
    }

    // Last resort: try to get from URL if we're on the editor page
    const currentUrl = window.location.href;
    if (currentUrl.includes('/editor')) {
      const urlMatch = currentUrl.match(/analysis\/([a-f0-9-]+)/);
      if (urlMatch) {
        console.log('🎯 Found analysis ID from URL:', urlMatch[1]);
        (window as any).currentAnalysisId = urlMatch[1];
        return urlMatch[1];
      }
    }

    return null;
  }

  // Get scene IDs from the story node graph via story timeline sync
  private getStorySceneIds(): string[] {
    try {
      // PRIORITY 1: Get from story timeline sync service (most accurate)
      const storyState = storyTimelineSync.getCurrentStoryState();
      console.log('🔍 DEBUG: Story timeline sync state:', {
        hasState: !!storyState,
        sceneCount: storyState?.sceneIds?.length || 0,
        sceneIds: storyState?.sceneIds || [],
        analysisId: storyState?.analysisId
      });

      if (storyState && storyState.sceneIds.length > 0) {
        console.log('📊 Scene IDs from story timeline sync (PRIORITY):', storyState.sceneIds);
        return storyState.sceneIds;
      } else {
        console.log('⚠️ Story timeline sync has no scenes, falling back to project data');
      }

      // SKIP PROJECT DATA MANAGER - it may have stale data during sync
      console.log('⚠️ Story timeline sync has no scenes - this means no scenes are in the story graph');
      console.log('🚫 Skipping project data manager fallback to avoid loading deleted scenes');

      // Return empty array if story timeline sync has no scenes
      // This means the story graph is empty and timeline should be empty too
      return [];

    } catch (error) {
      console.error('❌ Failed to get story scene IDs:', error);
      return [];
    }
  }

  // Clear timeline items (preserving AI-generated content)
  private clearAllTimelineItems(): void {
    try {
      console.log('🧹 GlobalTimelineSync: Clearing timeline items...');
      
      // This is a simplified version - in a real implementation, you'd need
      // access to the StateManager to properly clear timeline items
      // For now, we'll rely on the mezzanineLoader's force reload to handle this
      
      console.log('✅ GlobalTimelineSync: Timeline clearing initiated');
    } catch (error) {
      console.error('❌ Error clearing timeline items:', error);
    }
  }

  // Main sync function - replicates the exact logic from timeline header
  async syncTimeline(forceReload: boolean = false): Promise<void> {
    console.log('🔧 GlobalTimelineSync: handleSync called with forceReload:', forceReload);

    const analysisId = this.getCurrentAnalysisId();
    console.log('🔧 GlobalTimelineSync: analysisId:', analysisId);

    if (!analysisId) {
      console.warn('⚠️ No analysis ID available for sync');
      throw new Error('No analysis ID available for sync');
    }

    // 🎯 NEW: Get only scenes that exist in the story node graph
    const storySceneIds = this.getStorySceneIds();
    console.log('🎬 Story scene IDs found in node graph:', storySceneIds);

    if (storySceneIds.length === 0) {
      console.warn('⚠️ No scenes found in story node graph - nothing to sync');
      throw new Error('No scenes found in story node graph - nothing to sync');
    }

    try {
      // Clear the entire timeline before loading new content to prevent duplicates
      console.log('🧹 Clearing timeline before sync...');
      this.clearAllTimelineItems();

      // Small delay to ensure clearing is processed
      console.log('🔧 DEBUG: Waiting 200ms for clearing to complete...');
      await new Promise(resolve => setTimeout(resolve, 200));
      console.log('🔧 DEBUG: Wait complete, proceeding with loading...');

      // Create seamless transition configuration
      const seamlessConfig = createSeamlessTransitionConfig('frame-perfect', 30);

      const options = {
        analysisId,
        baseUrl: '',
        ...seamlessConfig,
        forceReload: true, // Always force reload since we cleared the timeline
        updateOnly: false, // Don't update, always load fresh since timeline is cleared
        customSceneOrder: storySceneIds, // 🎯 ONLY load scenes that exist in story graph
        onProgress: (loaded: number, total: number) => {
          console.log(`📊 GlobalTimelineSync: Story sync progress: ${loaded}/${total} scenes (from story graph)`);
        },
        onError: (error: string, sceneId?: string) => {
          console.error(`❌ GlobalTimelineSync: Story sync error${sceneId ? ` (Scene: ${sceneId})` : ''}:`, error);

          // Check if this is an analysis not found error
          if (error.includes('Analysis not found') || error.includes('404')) {
            // Show user-friendly message
            console.log('🔄 Analysis not found - user should upload a new video');
            // The error handling in mezzanineLoader will have already cleared stale data
          }
        },
        onComplete: (loadedCount: number) => {
          console.log(`🎉 GlobalTimelineSync: Story sync complete! Loaded ${loadedCount} scenes from story graph to timeline`);
        }
      };

      await loadMezzanineScenesToTimeline(options);
    } catch (error) {
      console.error('❌ GlobalTimelineSync: Sync failed:', error);
      throw error;
    }
  }

  // Force sync using the story timeline sync service
  forceSyncTimeline(): void {
    console.log('🔄 GlobalTimelineSync: Force syncing timeline via storyTimelineSync...');
    storyTimelineSync.forceSyncTimeline();
  }
}

// Create and export singleton instance
export const globalTimelineSync = GlobalTimelineSync.getInstance();

// Export default instance
export default globalTimelineSync;
